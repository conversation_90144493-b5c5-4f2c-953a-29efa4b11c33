# SuperNuggets Master

一款功能强大的Android自动化脚本应用，支持微信、支付宝、快手等多个平台的自动化操作。

## 项目简介

SuperNuggets Master是一个基于Android无障碍服务的自动化脚本执行平台，使用Kotlin + Jetpack Compose开发，采用MVVM架构模式，提供现代化的用户界面和强大的自动化功能。

## 主要功能

### 1. 脚本管理
- 支持多种平台脚本：微信、支付宝、快手
- 批量选择和执行脚本
- 脚本执行状态实时监控
- 执行进度可视化显示

### 2. 权限管理
- 无障碍服务权限检测和引导
- 媒体投影权限管理
- 悬浮窗权限检测
- 一站式权限设置界面

### 3. 自动化功能
- 基于OpenCV的图像识别
- 屏幕截图分析
- 节点定位和操作
- 手势模拟和点击操作

### 4. **调试工具 (新增)**
在用户完成所有权限配置后，主界面右上角会出现调试图标🐛，点击后可以启动全局悬浮调试工具：

#### 4.1 全局悬浮调试窗口
- **功能**: 通过WindowManager创建的全局悬浮窗，可以在任何应用上使用
- **特性**:
  - 即使应用在后台也可以调试其他应用
  - 悬浮红色圆形调试按钮，可拖拽移动
  - 点击调试按钮打开/关闭调试面板

#### 4.2 界面节点树分析
- **功能**: 获取当前显示界面的完整节点树结构，支持树形展开/折叠
- **使用方法**: 
  1. 点击悬浮调试按钮
  2. 选择"获取当前界面节点树"
  3. 查看当前界面所有UI元素的层次结构
  4. 点击展开/折叠按钮浏览子节点

#### 4.3 节点定位和高亮
- **功能**: 精确定位和高亮显示任意UI节点，支持全局覆盖
- **使用方法**:
  1. 在节点树列表中找到目标节点
  2. 点击节点右侧的定位图标📍
  3. 全局屏幕会用红色矩形框高亮显示该节点（可覆盖任何应用）

#### 4.4 节点信息详览
每个节点显示以下信息：
- **类名**: UI组件的完整类名
- **文本内容**: 节点包含的文本
- **位置信息**: 屏幕坐标和尺寸
- **交互属性**: 是否可点击、可滚动等
- **层级关系**: 在UI树中的层级位置
- **子节点数量**: 显示节点包含的子元素数量

#### 4.5 控制台日志输出
点击节点定位时，会在Android Studio的Logcat中输出详细的节点信息：
```
========== 节点信息 ==========
ID: TextView-L3-15
类名: android.widget.TextView
文本: 确认
描述: 确认按钮
ViewID: com.example.app:id/confirm_btn
位置: (100, 200) - (300, 250)
大小: 200 x 50
可点击: true
可滚动: false
可见: true
层级: 3
============================
```

## 技术架构

### 开发技术栈
- **语言**: Kotlin 100%
- **UI框架**: Jetpack Compose + Material Design 3
- **架构模式**: MVVM + Repository
- **依赖注入**: Hilt
- **数据库**: Room
- **图像处理**: OpenCV for Android
- **响应式编程**: Kotlin Flow + Coroutines

### 核心组件
- **ScriptEngine**: 脚本执行引擎
- **AccessibilityService**: 无障碍服务核心
- **PageRecognizer**: 页面识别和分析
- **DebugManager**: 调试工具管理器
- **ImageRecognitionHelper**: 图像识别助手
- **PermissionManager**: 权限管理器

## 项目结构

```
app/src/main/java/com/jerome/supernuggetsmaster/
├── annotation/          # 脚本注解
├── data/               # 数据模型
├── debug/              # 调试工具 (新增)
│   ├── DebugManager.kt               # 调试管理器
│   ├── FloatingDebugWindow.kt        # 悬浮调试窗口UI
│   ├── GlobalNodeHighlightOverlay.kt # 全局节点高亮组件
│   └── NodeHighlightOverlay.kt       # 应用内节点高亮组件
├── di/                 # 依赖注入
├── engine/             # 脚本引擎
├── permission/         # 权限管理
├── scripts/            # 具体脚本实现
├── service/            # 系统服务
│   ├── AutomationAccessibilityService.kt # 无障碍服务
│   ├── FloatingDebugService.kt           # 悬浮调试服务 (新增)
│   └── MediaProjectionService.kt         # 媒体投影服务
├── ui/                 # UI组件
└── utils/              # 工具类
```

## 安装和使用

### 环境要求
- Android 7.0 (API 24) 及以上
- 需要开启无障碍服务权限
- 需要授予屏幕录制权限

### 安装步骤
1. 下载并安装APK文件
2. 打开应用，按照引导开启必要权限
3. 选择需要执行的脚本
4. 点击开始执行

### 调试工具使用
1. 完成权限配置后，点击右上角🐛图标启动悬浮调试服务
2. 屏幕上会出现红色圆形悬浮调试按钮
3. 点击悬浮调试按钮打开调试面板
4. 选择"获取当前界面节点树"
5. 在树形节点列表中展开/折叠浏览节点结构
6. 点击📍图标定位任意节点（全局高亮）
7. 查看Logcat日志获取详细信息

## 开发特性

### 代码质量
- 100% Kotlin代码
- 遵循Material Design 3设计规范
- 完整的错误处理和日志记录
- 内存优化和生命周期管理

### 性能优化
- 使用Kotlin Flow进行响应式编程
- Coroutines处理异步操作
- 适配不同设备的自适应布局
- 电池使用优化

### 安全措施
- 代码混淆和安全防护
- 权限最小化原则
- 数据本地化存储

## 注意事项

### 使用须知
1. 本应用仅供学习和研究使用
2. 请遵守相关平台的使用条款
3. 不得用于违法违规活动
4. 使用前请备份重要数据

### 调试工具注意事项
1. 调试功能仅在开发和测试时使用
2. 节点信息可能包含敏感数据，请注意保护隐私
3. 频繁使用调试功能可能影响应用性能
4. 调试日志会记录在系统日志中

## 版本更新

### v1.2.0 (最新)
- ✨ 新增全局悬浮调试工具功能
- ✨ 添加树形节点树分析功能，支持展开/折叠
- ✨ 实现全局节点高亮定位（可覆盖任何应用）
- ✨ 支持详细的节点信息输出
- ✨ 通过WindowManager实现真正的全局调试体验
- 🔧 优化UI交互体验
- 🔧 改进权限管理流程

### v1.1.0
- 🔧 优化脚本执行引擎
- 🔧 改进图像识别准确性
- 🐛 修复权限检测问题

### v1.0.0
- 🎉 首次发布
- ✨ 基础脚本执行功能
- ✨ 权限管理系统
- ✨ 图像识别支持

## 开发者信息

- **开发者**: Jerome
- **技术支持**: 专业Android开发团队
- **架构设计**: 基于现代Android开发最佳实践

## 许可证

本项目遵循相关开源协议，仅供学习和研究使用。

---

> 💡 **提示**: 
> - 全局悬浮调试工具可以在任何应用上使用，即使本应用在后台运行！
> - 树形节点结构支持层层展开，方便分析复杂UI布局
> - 全局高亮功能可以精确定位任何应用中的UI元素
> - 这对于脚本开发和自动化测试非常有用！ 