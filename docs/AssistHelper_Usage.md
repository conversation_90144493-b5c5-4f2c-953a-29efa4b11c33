# AssistHelper 使用指南

## 概述

`AssistHelper` 是一个为脚本开发提供的高级自动化API，封装了Android无障碍服务的复杂操作，提供简洁易用的接口。

## 主要功能

### 1. 元素查找和操作

#### 通过文本查找元素
```kotlin
// 查找包含指定文本的元素
val element = AssistHelper.findByText("确定")

// 检查元素是否存在
if (element.exists()) {
    // 点击元素
    element.click()
}
```

#### 通过ID查找元素
```kotlin
// 通过资源ID查找元素
val element = AssistHelper.findById("com.example.app:id/button")

// 获取元素文本
val text = element.getText()
```

### 2. 滑动操作

```kotlin
// 向上滑动
AssistHelper.scrollUp()

// 向下滑动
AssistHelper.scrollDown()
```

### 3. 坐标点击

```kotlin
// 点击指定坐标
AssistHelper.clickCoordinates(500f, 800f)
```

### 4. 系统操作

```kotlin
// 返回键
AssistHelper.back()

// Home键
AssistHelper.home()
```

### 5. 等待操作

```kotlin
// 等待指定时间
AssistHelper.wait(2000) // 等待2秒

// 等待元素出现
val element = AssistHelper.waitForText("加载完成", timeoutMs = 10000)
if (element.exists()) {
    element.click()
}

// 等待ID元素出现
val element = AssistHelper.waitForId("com.example:id/button", timeoutMs = 5000)
```

### 6. 文本输入

```kotlin
// 向指定文本的元素输入内容
AssistHelper.inputText("Hello World", "请输入内容")

// 向指定ID的元素输入内容
AssistHelper.inputTextById("Hello World", "com.example:id/edittext")
```

## 完整示例

```kotlin
@ScriptPage(
    name = "微信首页",
    ids = ["com.tencent.mm:id/home_container", "com.tencent.mm:id/main_tab_layout"],
)
suspend fun mainPage() {
    // 检查服务是否可用
    if (!AssistHelper.isServiceAvailable()) {
        return
    }
    
    // 点击通讯录
    val contactsTab = AssistHelper.findByText("通讯录")
    if (contactsTab.exists()) {
        contactsTab.click()
        AssistHelper.wait(2000)
    }
    
    // 滑动操作
    AssistHelper.scrollUp()
    AssistHelper.wait(1000)
    
    // 等待特定元素出现
    val searchButton = AssistHelper.waitForText("搜索", timeoutMs = 5000)
    if (searchButton.exists()) {
        searchButton.click()
        
        // 输入搜索内容
        AssistHelper.inputText("朋友", "搜索")
        AssistHelper.wait(1000)
    }
    
    // 返回首页
    AssistHelper.back()
}
```

## API 参考

### ClickableElement 类

| 方法 | 描述 | 返回值 |
|------|------|--------|
| `click()` | 点击元素 | `Boolean` - 是否成功 |
| `exists()` | 检查元素是否存在 | `Boolean` |
| `getText()` | 获取元素文本 | `String?` |

### AssistHelper 对象方法

| 方法 | 描述 | 参数 | 返回值 |
|------|------|------|--------|
| `findByText(text)` | 通过文本查找元素 | `text: String` | `ClickableElement` |
| `findById(id)` | 通过ID查找元素 | `id: String` | `ClickableElement` |
| `scrollUp()` | 向上滑动 | 无 | `Boolean` |
| `scrollDown()` | 向下滑动 | 无 | `Boolean` |
| `clickCoordinates(x, y)` | 点击坐标 | `x: Float, y: Float` | `Boolean` |
| `back()` | 返回键 | 无 | `Boolean` |
| `home()` | Home键 | 无 | `Boolean` |
| `wait(ms)` | 等待 | `milliseconds: Long` | 无 |
| `waitForText(text, timeout, interval)` | 等待文本元素出现 | `text: String, timeoutMs: Long = 10000, checkIntervalMs: Long = 500` | `ClickableElement` |
| `waitForId(id, timeout, interval)` | 等待ID元素出现 | `id: String, timeoutMs: Long = 10000, checkIntervalMs: Long = 500` | `ClickableElement` |
| `isServiceAvailable()` | 检查服务是否可用 | 无 | `Boolean` |
| `inputText(text, target)` | 输入文本到指定元素 | `text: String, targetText: String` | `Boolean` |
| `inputTextById(text, id)` | 输入文本到指定ID元素 | `text: String, targetId: String` | `Boolean` |

## 最佳实践

### 1. 错误处理
```kotlin
// 总是检查元素是否存在
val element = AssistHelper.findByText("按钮")
if (element.exists()) {
    element.click()
} else {
    // 处理元素不存在的情况
    println("按钮未找到")
}
```

### 2. 合理使用等待
```kotlin
// 使用 wait() 而不是 delay()
AssistHelper.wait(1000) // 推荐

// 使用 waitForText() 等待元素出现
val element = AssistHelper.waitForText("加载完成")
```

### 3. 服务可用性检查
```kotlin
// 在脚本开始时检查服务是否可用
if (!AssistHelper.isServiceAvailable()) {
    println("无障碍服务未启用")
    return
}
```

### 4. 链式操作
```kotlin
// 可以链式调用
AssistHelper.findByText("设置").click()
AssistHelper.wait(1000)
AssistHelper.findByText("账号").click()
```

## 注意事项

1. **权限要求**: 使用 AssistHelper 需要启用无障碍服务权限
2. **异步操作**: 所有操作都是异步的，需要在 suspend 函数中调用
3. **元素查找**: 文本查找是模糊匹配，ID查找是精确匹配
4. **延迟处理**: 操作后会自动添加适当的延迟，无需手动添加过多延迟
5. **资源管理**: AssistHelper 会自动管理底层资源，无需手动释放

## 故障排除

### 常见问题

1. **元素找不到**: 检查文本是否正确，或者使用 ID 查找
2. **点击无效**: 确保元素可点击，或者尝试点击父元素
3. **服务不可用**: 检查无障碍服务是否已启用
4. **操作太快**: 增加等待时间或使用 waitForText()

### 调试技巧

```kotlin
// 检查元素是否存在
val element = AssistHelper.findByText("按钮")
println("元素存在: ${element.exists()}")
println("元素文本: ${element.getText()}")

// 检查服务状态
println("服务可用: ${AssistHelper.isServiceAvailable()}")
```
