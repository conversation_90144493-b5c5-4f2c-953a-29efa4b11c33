package com.jerome.supernuggetsmaster.scripts

import com.jerome.supernuggetsmaster.annotation.ScriptModel
import com.jerome.supernuggetsmaster.annotation.ScriptPage
import com.jerome.supernuggetsmaster.utils.AssistHelper
import kotlinx.coroutines.delay

@ScriptModel(
    id = "wechat",
    name = "暖暖孕迹",
    description = "获取免费使用时长",
    targetApp = "com.tencent.mm",
    icon = "💬",
    estimatedDuration = 240 // 秒为单位
)
class WeChatScript {

    @ScriptPage(
        name = "微信首页",
        ids = ["com.tencent.mm:id/jha", "com.tencent.mm:id/main_tab_layout"],
    )
    suspend fun mainPage() {
        // 检查服务是否可用
        if (!AssistHelper.isServiceAvailable()) {
            return
        }

        // 点击通讯录
        val contactsTab = AssistHelper.findByText("通讯录")
        if (contactsTab.exists()) {
            contactsTab.click()
            AssistHelper.wait(2000)
        }

        // 滑动操作
        AssistHelper.scrollUp()
        AssistHelper.wait(1000)
        AssistHelper.scrollUp()
        AssistHelper.wait(1000)

        // 点击"我"标签
        val meTab = AssistHelper.findByText("我")
        if (meTab.exists()) {
            meTab.click()
            AssistHelper.wait(2000)
        }

        // 点击"发现"标签
        val discoverTab = AssistHelper.findByText("发现")
        if (discoverTab.exists()) {
            discoverTab.click()
            AssistHelper.wait(2000)
        }

        // 返回首页
        val homeTab = AssistHelper.findByText("首页")
        if (homeTab.exists()) {
            homeTab.click()
            AssistHelper.wait(2000)
        }
    }
}