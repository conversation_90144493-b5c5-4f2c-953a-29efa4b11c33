package com.jerome.supernuggetsmaster.service

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner
import androidx.savedstate.SavedStateRegistry
import androidx.savedstate.SavedStateRegistryController
import androidx.savedstate.SavedStateRegistryOwner

/**
 * Service专用的LifecycleOwner
 * 为Service中的ComposeView提供生命周期管理
 */
class ServiceLifecycleOwner : LifecycleOwner, ViewModelStoreOwner, SavedStateRegistryOwner {
    
    private val lifecycleRegistry = LifecycleRegistry(this)
    private val _viewModelStore = ViewModelStore()
    private val savedStateRegistryController = SavedStateRegistryController.create(this)
    
    override val lifecycle: Lifecycle get() = lifecycleRegistry
    override val viewModelStore: ViewModelStore get() = _viewModelStore
    override val savedStateRegistry: SavedStateRegistry get() = savedStateRegistryController.savedStateRegistry
    
    init {
        savedStateRegistryController.performRestore(null)
    }
    
    fun onCreate() {
        lifecycleRegistry.currentState = Lifecycle.State.CREATED
    }
    
    fun onStart() {
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
    }
    
    fun onResume() {
        lifecycleRegistry.currentState = Lifecycle.State.RESUMED
    }
    
    fun onPause() {
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
    }
    
    fun onStop() {
        lifecycleRegistry.currentState = Lifecycle.State.CREATED
    }
    
    fun onDestroy() {
        lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
        _viewModelStore.clear()
    }
} 