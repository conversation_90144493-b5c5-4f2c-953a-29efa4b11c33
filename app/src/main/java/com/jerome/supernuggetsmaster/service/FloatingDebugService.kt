package com.jerome.supernuggetsmaster.service

import android.app.Service
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.IBinder
import android.view.Gravity
import android.view.WindowManager
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.BugReport
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.unit.dp
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.jerome.supernuggetsmaster.debug.DebugManager
import com.jerome.supernuggetsmaster.debug.FloatingDebugWindow
import com.jerome.supernuggetsmaster.debug.GlobalNodeHighlightOverlay
import com.jerome.supernuggetsmaster.utils.Logger
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * 全局悬浮调试服务
 * 通过WindowManager添加悬浮窗，可以在后台调试其他应用
 */
@AndroidEntryPoint
class FloatingDebugService : Service() {

    @Inject
    lateinit var debugManager: DebugManager

    @Inject
    lateinit var logger: Logger

    private lateinit var windowManager: WindowManager
    private var floatingView: ComposeView? = null
    private var debugWindowView: ComposeView? = null
    private var globalHighlightOverlay: GlobalNodeHighlightOverlay? = null
    private lateinit var serviceLifecycleOwner: ServiceLifecycleOwner

    companion object {
        private var instance: FloatingDebugService? = null
        
        fun getInstance(): FloatingDebugService? = instance
        
        fun isRunning(): Boolean = instance != null
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        
        // 初始化ServiceLifecycleOwner
        serviceLifecycleOwner = ServiceLifecycleOwner()
        serviceLifecycleOwner.onCreate()
        serviceLifecycleOwner.onStart()
        serviceLifecycleOwner.onResume()
        
        // 初始化全局高亮覆盖层
        globalHighlightOverlay = GlobalNodeHighlightOverlay(this, debugManager, logger, serviceLifecycleOwner)
        globalHighlightOverlay?.show()
        
        createFloatingIcon()
        logger.i("FloatingDebugService", "悬浮调试服务创建")
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 清理ServiceLifecycleOwner
        serviceLifecycleOwner.onPause()
        serviceLifecycleOwner.onStop()
        serviceLifecycleOwner.onDestroy()
        
        removeFloatingViews()
        globalHighlightOverlay?.hide()
        globalHighlightOverlay = null
        instance = null
        logger.i("FloatingDebugService", "悬浮调试服务销毁")
    }

    override fun onBind(intent: Intent?): IBinder? = null

    /**
     * 创建悬浮调试图标
     */
    private fun createFloatingIcon() {
        try {
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.END
                x = 20
                y = 100
            }

            floatingView = ComposeView(this).apply {
                setViewTreeLifecycleOwner(serviceLifecycleOwner)
                setViewTreeViewModelStoreOwner(serviceLifecycleOwner)
                setViewTreeSavedStateRegistryOwner(serviceLifecycleOwner)
                
                setContent {
                    FloatingDebugIcon(
                        onClick = { toggleDebugWindow() }
                    )
                }
            }

            windowManager.addView(floatingView, layoutParams)
            logger.d("FloatingDebugService", "悬浮调试图标创建成功")
        } catch (e: Exception) {
            logger.e("FloatingDebugService", "创建悬浮调试图标失败", e)
        }
    }

    /**
     * 切换调试窗口显示状态
     */
    private fun toggleDebugWindow() {
        if (debugWindowView == null) {
            showDebugWindow()
        } else {
            hideDebugWindow()
        }
    }

    /**
     * 显示调试窗口
     */
    private fun showDebugWindow() {
        try {
            val layoutParams = WindowManager.LayoutParams().apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.CENTER
            }

            debugWindowView = ComposeView(this).apply {
                setViewTreeLifecycleOwner(serviceLifecycleOwner)
                setViewTreeViewModelStoreOwner(serviceLifecycleOwner)
                setViewTreeSavedStateRegistryOwner(serviceLifecycleOwner)
                
                setContent {
                    FloatingDebugWindow(
                        debugManager = debugManager,
                        onClose = { hideDebugWindow() }
                    )
                }
            }

            windowManager.addView(debugWindowView, layoutParams)
            logger.d("FloatingDebugService", "调试窗口显示")
        } catch (e: Exception) {
            logger.e("FloatingDebugService", "显示调试窗口失败", e)
        }
    }

    /**
     * 隐藏调试窗口
     */
    private fun hideDebugWindow() {
        debugWindowView?.let { view ->
            try {
                windowManager.removeView(view)
                debugWindowView = null
                // 关闭调试窗口时清除高亮
                debugManager.clearHighlight()
                logger.d("FloatingDebugService", "调试窗口隐藏")
            } catch (e: Exception) {
                logger.e("FloatingDebugService", "隐藏调试窗口失败", e)
            }
        }
    }

    /**
     * 移除所有悬浮视图
     */
    private fun removeFloatingViews() {
        floatingView?.let { view ->
            try {
                windowManager.removeView(view)
                floatingView = null
            } catch (e: Exception) {
                logger.e("FloatingDebugService", "移除悬浮图标失败", e)
            }
        }
        
        hideDebugWindow()
    }
}

/**
 * 悬浮调试图标组件
 */
@Composable
private fun FloatingDebugIcon(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .size(56.dp)
            .clip(CircleShape)
            .background(Color.Red.copy(alpha = 0.8f))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = Icons.Default.BugReport,
            contentDescription = "调试工具",
            tint = Color.White,
            modifier = Modifier.size(28.dp)
        )
    }
} 