package com.jerome.supernuggetsmaster.utils

import android.view.accessibility.AccessibilityNodeInfo
import com.jerome.supernuggetsmaster.service.AutomationAccessibilityService
import kotlinx.coroutines.delay

/**
 * 自动化辅助工具类
 * 为脚本提供简洁的API，封装无障碍服务的复杂操作
 */
object AssistHelper {
    
    /**
     * 可点击的元素包装类
     */
    class ClickableElement(internal val node: AccessibilityNodeInfo?) {
        
        /**
         * 执行点击操作
         * @return 是否点击成功
         */
        suspend fun click(): Boolean {
            return if (node != null) {
                val service = AutomationAccessibilityService.getInstance()
                if (service != null) {
                    val success = service.clickNode(node)
                    if (success) {
                        // 点击后稍微延迟，等待界面响应
                        delay(500)
                    }
                    success
                } else {
                    false
                }
            } else {
                false
            }
        }
        
        /**
         * 检查元素是否存在
         */
        fun exists(): Boolean = node != null
        
        /**
         * 获取元素文本
         */
        fun getText(): String? = node?.text?.toString()
    }
    
    /**
     * 通过文本查找元素
     * @param text 要查找的文本
     * @return 可点击的元素，如果未找到则返回空元素
     */
    fun findByText(text: String): ClickableElement {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val node = service.findNodeByText(text)
            ClickableElement(node)
        } else {
            ClickableElement(null)
        }
    }
    
    /**
     * 通过ID查找元素
     * @param id 要查找的资源ID
     * @return 可点击的元素，如果未找到则返回空元素
     */
    fun findById(id: String): ClickableElement {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val node = service.findNodeById(id)
            ClickableElement(node)
        } else {
            ClickableElement(null)
        }
    }
    
    /**
     * 向上滑动
     * @return 是否滑动成功
     */
    suspend fun scrollUp(): Boolean {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val success = service.swipeUp()
            if (success) {
                // 滑动后延迟，等待界面稳定
                delay(1000)
            }
            success
        } else {
            false
        }
    }
    
    /**
     * 向下滑动
     * @return 是否滑动成功
     */
    suspend fun scrollDown(): Boolean {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val success = service.swipeDown()
            if (success) {
                // 滑动后延迟，等待界面稳定
                delay(1000)
            }
            success
        } else {
            false
        }
    }
    
    /**
     * 点击坐标
     * @param x X坐标
     * @param y Y坐标
     * @return 是否点击成功
     */
    suspend fun clickCoordinates(x: Float, y: Float): Boolean {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val success = service.clickByCoordinates(x, y)
            if (success) {
                // 点击后延迟，等待界面响应
                delay(500)
            }
            success
        } else {
            false
        }
    }
    
    /**
     * 返回操作
     * @return 是否操作成功
     */
    suspend fun back(): Boolean {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val success = service.performBack()
            if (success) {
                delay(500)
            }
            success
        } else {
            false
        }
    }
    
    /**
     * Home键操作
     * @return 是否操作成功
     */
    suspend fun home(): Boolean {
        val service = AutomationAccessibilityService.getInstance()
        return if (service != null) {
            val success = service.performHome()
            if (success) {
                delay(500)
            }
            success
        } else {
            false
        }
    }
    
    /**
     * 等待指定时间
     * @param milliseconds 等待时间（毫秒）
     */
    suspend fun wait(milliseconds: Long) {
        delay(milliseconds)
    }
    
    /**
     * 等待元素出现
     * @param text 要等待的文本
     * @param timeoutMs 超时时间（毫秒），默认10秒
     * @param checkIntervalMs 检查间隔（毫秒），默认500毫秒
     * @return 找到的元素，如果超时则返回空元素
     */
    suspend fun waitForText(
        text: String, 
        timeoutMs: Long = 10000, 
        checkIntervalMs: Long = 500
    ): ClickableElement {
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val element = findByText(text)
            if (element.exists()) {
                return element
            }
            delay(checkIntervalMs)
        }
        
        return ClickableElement(null)
    }
    
    /**
     * 等待元素出现（通过ID）
     * @param id 要等待的资源ID
     * @param timeoutMs 超时时间（毫秒），默认10秒
     * @param checkIntervalMs 检查间隔（毫秒），默认500毫秒
     * @return 找到的元素，如果超时则返回空元素
     */
    suspend fun waitForId(
        id: String, 
        timeoutMs: Long = 10000, 
        checkIntervalMs: Long = 500
    ): ClickableElement {
        val startTime = System.currentTimeMillis()
        
        while (System.currentTimeMillis() - startTime < timeoutMs) {
            val element = findById(id)
            if (element.exists()) {
                return element
            }
            delay(checkIntervalMs)
        }
        
        return ClickableElement(null)
    }
    
    /**
     * 检查无障碍服务是否可用
     * @return 服务是否可用
     */
    fun isServiceAvailable(): Boolean {
        return AutomationAccessibilityService.getInstance() != null
    }
    
    /**
     * 输入文本到指定元素
     * @param text 要输入的文本
     * @param targetText 目标元素的文本（用于查找元素）
     * @return 是否输入成功
     */
    suspend fun inputText(text: String, targetText: String): Boolean {
        val element = findByText(targetText)
        return if (element.exists() && element.node != null) {
            val service = AutomationAccessibilityService.getInstance()
            service?.inputText(element.node, text) ?: false
        } else {
            false
        }
    }
    
    /**
     * 输入文本到指定ID的元素
     * @param text 要输入的文本
     * @param targetId 目标元素的ID
     * @return 是否输入成功
     */
    suspend fun inputTextById(text: String, targetId: String): Boolean {
        val element = findById(targetId)
        return if (element.exists() && element.node != null) {
            val service = AutomationAccessibilityService.getInstance()
            service?.inputText(element.node, text) ?: false
        } else {
            false
        }
    }
}
