package com.jerome.supernuggetsmaster.debug

import android.graphics.Rect
import android.view.accessibility.AccessibilityNodeInfo
import com.jerome.supernuggetsmaster.service.AutomationAccessibilityService
import com.jerome.supernuggetsmaster.utils.Logger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 调试节点信息数据类 - 支持树形结构展开折叠
 */
data class DebugNodeInfo(
    val id: String,
    val className: String?,
    val text: String?,
    val contentDescription: String?,
    val viewId: String?,
    val bounds: Rect,
    val isClickable: Boolean,
    val isScrollable: Boolean,
    val isVisible: Boolean,
    val level: Int,
    val children: List<DebugNodeInfo> = emptyList(),
    val actualNode: AccessibilityNodeInfo? = null,
    val isExpanded: Boolean = false,
    val hasChildren: Boolean = false
)

/**
 * 调试管理器
 * 负责获取节点树、管理调试状态和节点高亮功能
 */
@Singleton
class DebugManager @Inject constructor(
    private val logger: Logger
) {
    private val _isDebugMode = MutableStateFlow(false)
    val isDebugMode: StateFlow<Boolean> = _isDebugMode.asStateFlow()

    private val _rootNodes = MutableStateFlow<List<DebugNodeInfo>>(emptyList())
    val rootNodes: StateFlow<List<DebugNodeInfo>> = _rootNodes.asStateFlow()
    
    private val _visibleNodes = MutableStateFlow<List<DebugNodeInfo>>(emptyList())
    val visibleNodes: StateFlow<List<DebugNodeInfo>> = _visibleNodes.asStateFlow()
    
    private val _expandedNodeIds = MutableStateFlow<Set<String>>(emptySet())
    val expandedNodeIds: StateFlow<Set<String>> = _expandedNodeIds.asStateFlow()

    private val _highlightedNode = MutableStateFlow<DebugNodeInfo?>(null)
    val highlightedNode: StateFlow<DebugNodeInfo?> = _highlightedNode.asStateFlow()



    /**
     * 开启/关闭调试模式
     */
    fun toggleDebugMode() {
        _isDebugMode.value = !_isDebugMode.value
        if (!_isDebugMode.value) {
            _highlightedNode.value = null
            _rootNodes.value = emptyList()
            _visibleNodes.value = emptyList()
            _expandedNodeIds.value = emptySet()
        }
        logger.i("DebugManager", "调试模式: ${_isDebugMode.value}")
    }



    /**
     * 获取当前界面节点树
     */
    fun getCurrentNodeTree() {
        val service = AutomationAccessibilityService.getInstance()
        if (service == null) {
            logger.w("DebugManager", "无障碍服务未启动，无法获取节点树")
            return
        }

        val rootNode = service.getRootNode()
        if (rootNode == null) {
            logger.w("DebugManager", "无法获取根节点")
            return
        }

        val rootNodes = mutableListOf<DebugNodeInfo>()
        val nodeCount = buildNodeTree(rootNode, rootNodes, 0)
        _rootNodes.value = rootNodes
        
        // 重新计算可见节点
        updateVisibleNodes()
        
        logger.i("DebugManager", "获取到节点树，共${nodeCount}个节点")
    }

    /**
     * 构建节点树结构
     */
    private fun buildNodeTree(
        node: AccessibilityNodeInfo,
        result: MutableList<DebugNodeInfo>,
        level: Int
    ): Int {
        var totalCount = 0
        try {
            val bounds = Rect()
            node.getBoundsInScreen(bounds)

            // 递归构建子节点
            val children = mutableListOf<DebugNodeInfo>()
            for (i in 0 until node.childCount) {
                val child = node.getChild(i)
                if (child != null) {
                    totalCount += buildNodeTree(child, children, level + 1)
                }
            }

            val nodeInfo = DebugNodeInfo(
                id = generateNodeId(node, level, totalCount),
                className = node.className?.toString(),
                text = node.text?.toString()?.takeIf { it.isNotEmpty() },
                contentDescription = node.contentDescription?.toString()?.takeIf { it.isNotEmpty() },
                viewId = node.viewIdResourceName,
                bounds = bounds,
                isClickable = node.isClickable,
                isScrollable = node.isScrollable,
                isVisible = node.isVisibleToUser,
                level = level,
                children = children,
                actualNode = node,
                hasChildren = children.isNotEmpty(),
                isExpanded = false
            )

            result.add(nodeInfo)
            totalCount += 1
        } catch (e: Exception) {
            logger.e("DebugManager", "构建节点树失败", e)
        }
        return totalCount
    }

    /**
     * 更新可见节点列表（根据展开状态）
     */
    private fun updateVisibleNodes() {
        val visibleList = mutableListOf<DebugNodeInfo>()
        val expandedIds = _expandedNodeIds.value
        
        fun addVisibleNodes(nodes: List<DebugNodeInfo>) {
            for (node in nodes) {
                visibleList.add(node)
                if (node.hasChildren && expandedIds.contains(node.id)) {
                    addVisibleNodes(node.children)
                }
            }
        }
        
        addVisibleNodes(_rootNodes.value)
        _visibleNodes.value = visibleList
    }

    /**
     * 切换节点展开/折叠状态
     */
    fun toggleNodeExpansion(nodeId: String) {
        val currentExpanded = _expandedNodeIds.value.toMutableSet()
        if (currentExpanded.contains(nodeId)) {
            currentExpanded.remove(nodeId)
        } else {
            currentExpanded.add(nodeId)
        }
        _expandedNodeIds.value = currentExpanded
        updateVisibleNodes()
        logger.d("DebugManager", "切换节点展开状态: $nodeId")
    }

    /**
     * 生成节点ID
     */
    private fun generateNodeId(node: AccessibilityNodeInfo, level: Int, index: Int): String {
        val className = node.className?.toString()?.substringAfterLast('.') ?: "Unknown"
        return "$className-L$level-$index"
    }

    /**
     * 高亮显示指定节点
     */
    fun highlightNode(nodeInfo: DebugNodeInfo) {
        _highlightedNode.value = nodeInfo
        
        // 在控制台打印节点信息
        printNodeInfo(nodeInfo)
        
        logger.d("DebugManager", "高亮节点: ${nodeInfo.id}")
    }

    /**
     * 清除节点高亮
     */
    fun clearHighlight() {
        _highlightedNode.value = null
        logger.d("DebugManager", "清除节点高亮")
    }

    /**
     * 在控制台打印节点详细信息
     */
    private fun printNodeInfo(nodeInfo: DebugNodeInfo) {
        val info = buildString {
            appendLine("========== 节点信息 ==========")
            appendLine("ID: ${nodeInfo.id}")
            appendLine("类名: ${nodeInfo.className ?: "无"}")
            appendLine("文本: ${nodeInfo.text ?: "无"}")
            appendLine("描述: ${nodeInfo.contentDescription ?: "无"}")
            appendLine("ViewID: ${nodeInfo.viewId ?: "无"}")
            appendLine("位置: (${nodeInfo.bounds.left}, ${nodeInfo.bounds.top}) - (${nodeInfo.bounds.right}, ${nodeInfo.bounds.bottom})")
            appendLine("大小: ${nodeInfo.bounds.width()} x ${nodeInfo.bounds.height()}")
            appendLine("可点击: ${nodeInfo.isClickable}")
            appendLine("可滚动: ${nodeInfo.isScrollable}")
            appendLine("可见: ${nodeInfo.isVisible}")
            appendLine("层级: ${nodeInfo.level}")
            appendLine("============================")
        }
        
        logger.i("DebugManager", info)
        println(info) // 同时输出到系统控制台
    }

    /**
     * 点击节点
     */
    fun clickNode(nodeInfo: DebugNodeInfo) {
        val service = AutomationAccessibilityService.getInstance()
        if (service == null) {
            logger.w("DebugManager", "无障碍服务未启动，无法点击节点")
            return
        }

        nodeInfo.actualNode?.let { node ->
            val success = service.clickNode(node)
            logger.i("DebugManager", "点击节点 ${nodeInfo.id}: ${if (success) "成功" else "失败"}")
        }
    }
}