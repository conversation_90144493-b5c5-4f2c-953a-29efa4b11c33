package com.jerome.supernuggetsmaster.debug

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp

/**
 * 节点高亮覆盖层
 * 用于在屏幕上绘制矩形框高亮显示选中的节点
 */
@Composable
fun NodeHighlightOverlay(
    debugManager: DebugManager,
    modifier: Modifier = Modifier
) {
    val highlightedNode by debugManager.highlightedNode.collectAsState()
    val density = LocalDensity.current
    
    val highlightColor = Color.Red
    val strokeWidth = with(density) { 3.dp.toPx() }
    
    Canvas(modifier = modifier.fillMaxSize()) {
        highlightedNode?.let { node ->
            // 绘制高亮矩形框
            drawRect(
                color = highlightColor,
                topLeft = androidx.compose.ui.geometry.Offset(
                    x = node.bounds.left.toFloat(),
                    y = node.bounds.top.toFloat()
                ),
                size = androidx.compose.ui.geometry.Size(
                    width = node.bounds.width().toFloat(),
                    height = node.bounds.height().toFloat()
                ),
                style = Stroke(width = strokeWidth)
            )
            
            // 绘制半透明填充
            drawRect(
                color = highlightColor.copy(alpha = 0.2f),
                topLeft = androidx.compose.ui.geometry.Offset(
                    x = node.bounds.left.toFloat(),
                    y = node.bounds.top.toFloat()
                ),
                size = androidx.compose.ui.geometry.Size(
                    width = node.bounds.width().toFloat(),
                    height = node.bounds.height().toFloat()
                )
            )
        }
    }
} 