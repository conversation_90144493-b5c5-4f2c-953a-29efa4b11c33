package com.jerome.supernuggetsmaster.debug

import android.app.Service
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.WindowManager
import com.jerome.supernuggetsmaster.service.ServiceLifecycleOwner
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.lifecycle.setViewTreeLifecycleOwner
import androidx.lifecycle.setViewTreeViewModelStoreOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import com.jerome.supernuggetsmaster.utils.Logger

/**
 * 全局节点高亮覆盖层管理器
 * 动态创建小的悬浮窗来高亮特定节点，避免全屏覆盖
 */
class GlobalNodeHighlightOverlay(
    private val service: Service,
    private val debugManager: DebugManager,
    private val logger: Logger,
    private val serviceLifecycleOwner: ServiceLifecycleOwner
) {
    private lateinit var windowManager: WindowManager
    private var overlayView: ComposeView? = null
    private var currentHighlightedNode: DebugNodeInfo? = null

    init {
        windowManager = service.getSystemService(Service.WINDOW_SERVICE) as WindowManager
        
        // 监听高亮节点变化
        CoroutineScope(Dispatchers.Main).launch {
            debugManager.highlightedNode.collect { node ->
                if (node != currentHighlightedNode) {
                    currentHighlightedNode = node
                    updateHighlight()
                }
            }
        }
    }

    /**
     * 显示高亮覆盖层（空实现，实际通过监听器触发）
     */
    fun show() {
        // 不需要预先创建，监听器会自动处理
    }

    /**
     * 隐藏高亮覆盖层
     */
    fun hide() {
        hideCurrentOverlay()
    }

    /**
     * 更新高亮显示
     */
    private fun updateHighlight() {
        // 先隐藏当前覆盖层
        hideCurrentOverlay()
        
        // 如果有新的高亮节点，创建新的覆盖层
        currentHighlightedNode?.let { node ->
            showHighlightForNode(node)
        }
    }

    /**
     * 为特定节点显示高亮
     */
    private fun showHighlightForNode(node: DebugNodeInfo) {
        try {
            val layoutParams = WindowManager.LayoutParams().apply {
                width = node.bounds.width() + 20  // 比节点稍大一点
                height = node.bounds.height() + 20
                type = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                } else {
                    @Suppress("DEPRECATION")
                    WindowManager.LayoutParams.TYPE_PHONE
                }
                flags = WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
                x = node.bounds.left - 10  // 向左上偏移一点
                y = node.bounds.top - 10
            }

            overlayView = ComposeView(service).apply {
                setViewTreeLifecycleOwner(serviceLifecycleOwner)
                setViewTreeViewModelStoreOwner(serviceLifecycleOwner)
                setViewTreeSavedStateRegistryOwner(serviceLifecycleOwner)
                
                setContent {
                    NodeHighlightBox(node = node)
                }
            }

            windowManager.addView(overlayView, layoutParams)
            logger.d("GlobalNodeHighlightOverlay", "节点高亮覆盖层显示: ${node.id}")
        } catch (e: Exception) {
            logger.e("GlobalNodeHighlightOverlay", "显示节点高亮覆盖层失败", e)
        }
    }

    /**
     * 隐藏当前覆盖层
     */
    private fun hideCurrentOverlay() {
        overlayView?.let { view ->
            try {
                windowManager.removeView(view)
                overlayView = null
                logger.d("GlobalNodeHighlightOverlay", "节点高亮覆盖层隐藏")
            } catch (e: Exception) {
                logger.e("GlobalNodeHighlightOverlay", "隐藏节点高亮覆盖层失败", e)
            }
        }
    }
}

/**
 * 节点高亮框组件
 * 绘制特定节点的高亮矩形框
 */
@Composable
private fun NodeHighlightBox(
    node: DebugNodeInfo,
    modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val highlightColor = Color.Red
    val strokeWidth = with(density) { 4.dp.toPx() }
    
    Canvas(modifier = modifier.fillMaxSize()) {
        // 绘制高亮矩形框（相对于当前悬浮窗的坐标）
        drawRect(
            color = highlightColor,
            topLeft = androidx.compose.ui.geometry.Offset(
                x = 10f,  // 对应于layoutParams中的偏移
                y = 10f
            ),
            size = androidx.compose.ui.geometry.Size(
                width = node.bounds.width().toFloat(),
                height = node.bounds.height().toFloat()
            ),
            style = Stroke(width = strokeWidth)
        )
        
        // 绘制半透明填充
        drawRect(
            color = highlightColor.copy(alpha = 0.15f),
            topLeft = androidx.compose.ui.geometry.Offset(
                x = 10f,
                y = 10f
            ),
            size = androidx.compose.ui.geometry.Size(
                width = node.bounds.width().toFloat(),
                height = node.bounds.height().toFloat()
            )
        )
    }
} 