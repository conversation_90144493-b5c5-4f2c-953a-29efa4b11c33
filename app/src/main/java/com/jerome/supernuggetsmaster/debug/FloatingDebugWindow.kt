package com.jerome.supernuggetsmaster.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 悬浮调试窗口组件
 * 支持树形结构的节点展开和折叠
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FloatingDebugWindow(
    debugManager: DebugManager,
    onClose: () -> Unit,
    modifier: Modifier = Modifier
) {
    val visibleNodes by debugManager.visibleNodes.collectAsState()
    val expandedNodeIds by debugManager.expandedNodeIds.collectAsState()
    var showNodeTree by remember { mutableStateOf(false) }

    Card(
        modifier = modifier
            .width(350.dp)
            .height(500.dp),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(MaterialTheme.colorScheme.primary)
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "调试工具",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimary
                )
                IconButton(onClick = onClose) {
                    Icon(
                        imageVector = Icons.Default.Close,
                        contentDescription = "关闭",
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            }

            if (!showNodeTree) {
                // 调试功能菜单
                DebugFunctionMenu(
                    onGetNodeTree = {
                        debugManager.getCurrentNodeTree()
                        showNodeTree = true
                    },
                    modifier = Modifier.weight(1f)
                )
            } else {
                // 节点树列表
                TreeNodeList(
                    visibleNodes = visibleNodes,
                    expandedNodeIds = expandedNodeIds,
                    onNodeClick = { nodeInfo ->
                        debugManager.highlightNode(nodeInfo)
                    },
                    onExpandToggle = { nodeId ->
                        debugManager.toggleNodeExpansion(nodeId)
                    },
                    onBack = { showNodeTree = false },
                    onRefresh = { debugManager.getCurrentNodeTree() },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * 调试功能菜单
 */
@Composable
private fun DebugFunctionMenu(
    onGetNodeTree: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            DebugMenuItem(
                title = "获取当前界面节点树",
                description = "分析当前显示界面的所有UI节点结构",
                onClick = onGetNodeTree
            )
        }
        
        item {
            DebugMenuItem(
                title = "截图分析",
                description = "获取当前屏幕截图并进行分析（暂未实现）",
                onClick = { /* TODO: 实现截图功能 */ },
                enabled = false
            )
        }
        
        item {
            DebugMenuItem(
                title = "手势录制",
                description = "录制用户手势操作（暂未实现）",
                onClick = { /* TODO: 实现手势录制 */ },
                enabled = false
            )
        }
    }
}

/**
 * 调试菜单项
 */
@Composable
private fun DebugMenuItem(
    title: String,
    description: String,
    onClick: () -> Unit,
    enabled: Boolean = true,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onClick() },
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (enabled) MaterialTheme.colorScheme.surface 
                           else MaterialTheme.colorScheme.surface.copy(alpha = 0.6f)
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = if (enabled) MaterialTheme.colorScheme.onSurface 
                       else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = if (enabled) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                       else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.4f)
            )
        }
    }
}

/**
 * 树形节点列表
 */
@Composable
private fun TreeNodeList(
    visibleNodes: List<DebugNodeInfo>,
    expandedNodeIds: Set<String>,
    onNodeClick: (DebugNodeInfo) -> Unit,
    onExpandToggle: (String) -> Unit,
    onBack: () -> Unit,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        // 操作栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            TextButton(onClick = onBack) {
                Text("← 返回")
            }
            
            Text(
                text = "节点树 (${visibleNodes.size})",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            IconButton(onClick = onRefresh) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新"
                )
            }
        }

        HorizontalDivider()

        // 节点列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(visibleNodes) { nodeInfo ->
                TreeNodeItem(
                    nodeInfo = nodeInfo,
                    isExpanded = expandedNodeIds.contains(nodeInfo.id),
                    onClick = { onNodeClick(nodeInfo) },
                    onExpandToggle = { onExpandToggle(nodeInfo.id) }
                )
            }
        }
    }
}

/**
 * 树形节点项
 */
@Composable
private fun TreeNodeItem(
    nodeInfo: DebugNodeInfo,
    isExpanded: Boolean,
    onClick: () -> Unit,
    onExpandToggle: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 缩进和展开/折叠按钮
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 缩进
                repeat(nodeInfo.level) {
                    Spacer(modifier = Modifier.width(20.dp))
                }
                
                // 展开/折叠按钮
                if (nodeInfo.hasChildren) {
                    IconButton(
                        onClick = onExpandToggle,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = if (isExpanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                            contentDescription = if (isExpanded) "折叠" else "展开",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.width(24.dp))
                }
            }
            
            // 节点信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // 类名和状态
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = nodeInfo.className?.substringAfterLast('.') ?: "Unknown",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.weight(1f, false)
                    )
                    
                    // 子节点数量提示
                    if (nodeInfo.hasChildren) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "(${nodeInfo.children.size})",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                    
                    // 可点击标识
                    if (nodeInfo.isClickable) {
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "可点击",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier
                                .background(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                    RoundedCornerShape(4.dp)
                                )
                                .padding(horizontal = 4.dp, vertical = 2.dp)
                        )
                    }
                }
                
                // 文本内容
                if (!nodeInfo.text.isNullOrEmpty()) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "\"${nodeInfo.text}\"",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
                
                // 位置信息
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = "(${nodeInfo.bounds.left},${nodeInfo.bounds.top}) ${nodeInfo.bounds.width()}×${nodeInfo.bounds.height()}",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f)
                )
            }
            
            // 定位按钮
            IconButton(
                onClick = onClick,
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.LocationOn,
                    contentDescription = "定位节点",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(18.dp)
                )
            }
        }
    }
} 